import { supabase } from './supabase'
import { Database } from './database.types'

export type Product = Database['public']['Tables']['products']['Row'] & {
  category?: Database['public']['Tables']['categories']['Row']
  images?: Database['public']['Tables']['product_images']['Row'][]
  inventory?: Database['public']['Tables']['inventory']['Row']
}

export type Category = Database['public']['Tables']['categories']['Row']

export const getProducts = async (filters?: {
  category?: string
  featured?: boolean
  search?: string
  limit?: number
  offset?: number
}) => {
  let query = supabase
    .from('products')
    .select(`
      *,
      category:categories(*),
      images:product_images(*),
      inventory(*)
    `)
    .eq('is_active', true)
    .order('created_at', { ascending: false })

  if (filters?.category) {
    query = query.eq('category_id', filters.category)
  }

  if (filters?.featured) {
    query = query.eq('is_featured', true)
  }

  if (filters?.search) {
    query = query.or(`name.ilike.%${filters.search}%,description.ilike.%${filters.search}%`)
  }

  if (filters?.limit) {
    query = query.limit(filters.limit)
  }

  if (filters?.offset) {
    query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1)
  }

  const { data, error } = await query

  if (error) throw error
  return data as Product[]
}

export const getProduct = async (slug: string) => {
  const { data, error } = await supabase
    .from('products')
    .select(`
      *,
      category:categories(*),
      images:product_images(*),
      inventory(*),
      reviews(*, profiles(first_name, last_name))
    `)
    .eq('slug', slug)
    .eq('is_active', true)
    .single()

  if (error) throw error
  return data
}

export const getFeaturedProducts = async (limit = 8) => {
  return getProducts({ featured: true, limit })
}

export const getCategories = async () => {
  const { data, error } = await supabase
    .from('categories')
    .select('*')
    .eq('is_active', true)
    .order('sort_order')

  if (error) throw error
  return data as Category[]
}

export const searchProducts = async (query: string, limit = 20) => {
  return getProducts({ search: query, limit })
}

export const getProductsByCategory = async (categorySlug: string, limit = 20) => {
  const { data: category } = await supabase
    .from('categories')
    .select('id')
    .eq('slug', categorySlug)
    .single()

  if (!category) return []
  
  return getProducts({ category: category.id, limit })
}