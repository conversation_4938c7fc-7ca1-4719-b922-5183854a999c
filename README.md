# 3dunyam.com - 3D Printing E-commerce Platform

A comprehensive e-commerce platform built with Next.js and Supabase, specifically designed for 3D printing products targeting the Turkish market.

## 🚀 Features

### Customer Features
- Modern, responsive design optimized for Turkish users
- Product catalog with advanced filtering (category, price, material type)
- Individual product pages with detailed specifications and image galleries
- Shopping cart functionality with persistent storage
- User authentication (register/login) with profile management
- Order history and tracking
- Product search with Turkish language support
- Mobile-optimized experience

### Admin Features
- Comprehensive admin dashboard with sales analytics
- Product management (add, edit, delete, bulk operations)
- Inventory management with stock tracking
- Order management system with status updates
- User management and role assignment
- Category management with hierarchical structure
- Image upload functionality
- Real-time notifications

## 🛠 Technology Stack

- **Frontend**: Next.js 13+ (App Router), TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Authentication, Storage)
- **UI Components**: shadcn/ui, Radix UI
- **Forms**: React Hook Form with Zod validation
- **Icons**: Lucide React
- **Charts**: Recharts
- **Notifications**: React Hot Toast

## 📁 Project Structure

```
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication pages
│   ├── products/          # Product catalog pages
│   ├── admin/             # Admin panel pages
│   └── globals.css        # Global styles
├── components/            # Reusable components
│   ├── auth/              # Authentication components
│   ├── layout/            # Layout components (Header, Footer)
│   ├── product/           # Product-related components
│   ├── admin/             # Admin panel components
│   └── ui/                # shadcn/ui components
├── lib/                   # Utility functions and services
│   ├── auth.ts            # Authentication helpers
│   ├── products.ts        # Product management
│   ├── cart.ts            # Shopping cart logic
│   ├── supabase.ts        # Supabase client
│   └── database.types.ts  # TypeScript database types
├── supabase/              # Database migrations and configuration
│   └── migrations/        # SQL migration files
└── public/                # Static assets
```

## 🗄 Database Schema

The platform uses a comprehensive database schema including:

- **profiles**: Extended user profiles with Turkish address fields
- **categories**: Hierarchical product categories
- **products**: Main product catalog with 3D printing specifications
- **product_images**: Multiple images per product
- **inventory**: Real-time stock tracking
- **carts & cart_items**: Persistent shopping cart
- **orders & order_items**: Complete order management
- **reviews**: Product reviews and ratings

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd 3dunyam
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   - Create a new Supabase project
   - Copy the project URL and anon key
   - Run the migration files in the Supabase SQL editor

4. **Configure environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Fill in your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
   ```

5. **Run database migrations**
   - Go to your Supabase project dashboard
   - Navigate to SQL Editor
   - Run the migration files in order:
     - `create_initial_schema.sql`
     - `seed_initial_data.sql`

6. **Start the development server**
   ```bash
   npm run dev
   ```

7. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔧 Configuration

### Supabase Setup

1. **Row Level Security (RLS)**
   - All tables have RLS enabled
   - Customers can only access their own data
   - Admins have full access to manage the platform

2. **Authentication**
   - Email/password authentication
   - Automatic profile creation on signup
   - Role-based access control

3. **Storage**
   - Product images stored in Supabase Storage
   - Automatic image optimization
   - CDN delivery for fast loading

### Admin Account

To create an admin account:

1. Register a normal account through the UI
2. In Supabase, go to Table Editor > profiles
3. Find your user and change the `role` column to `'admin'`
4. You'll now have access to the admin panel at `/admin`

## 🎨 Design System

The platform uses a consistent design system with:

- **Colors**: Primary blue (#2563eb), secondary teal (#0d9488), accent orange (#ea580c)
- **Typography**: Inter font family with Turkish character support
- **Components**: Consistent spacing (8px system), rounded corners, subtle shadows
- **Responsive**: Mobile-first approach with breakpoints for tablet and desktop
- **Accessibility**: WCAG 2.1 compliant with proper contrast ratios

## 📱 Mobile Optimization

- Responsive design that works on all screen sizes
- Touch-friendly interface elements
- Optimized images and loading states
- Mobile-specific navigation patterns
- Progressive Web App (PWA) ready

## 🔒 Security Features

- Row Level Security (RLS) for all database operations
- Input validation with Zod schemas
- SQL injection protection
- XSS protection
- CSRF protection
- Secure authentication with Supabase Auth

## 🚀 Performance Optimizations

- Next.js Image optimization
- Lazy loading for product images
- Database query optimization with proper indexes
- Caching strategies for product data
- Code splitting and bundle optimization

## 📊 Analytics & Monitoring

The admin dashboard includes:
- Sales analytics with charts
- Product performance metrics
- User engagement statistics
- Inventory alerts
- Order status tracking

## 🛒 E-commerce Features

- Multi-step checkout process
- Inventory management with stock alerts
- Order status tracking
- Product reviews and ratings
- Wishlist functionality
- Promotional codes and discounts (ready for implementation)

## 🌐 Localization

- Turkish language support throughout the interface
- Turkish currency (TRY) formatting
- Local address formats
- Culturally appropriate design patterns

## 📈 Future Enhancements

- Payment integration with Turkish payment providers
- Advanced search with filters
- Product recommendations
- Email notifications
- Multi-vendor support
- Mobile app development
- Advanced analytics and reporting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

## 🙏 Acknowledgments

- Built with Next.js and Supabase
- UI components from shadcn/ui
- Icons from Lucide React
- Images from Pexels (for demo purposes)