/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'fzflsyxsqimtxfbuzipz.supabase.co',
        port: '',
        pathname: '/storage/v1/object/public/**',
      },
    ],
  },
  experimental: {
    // Enable new caching and pre-rendering behavior (Next.js 15.4+)
    dynamicIO: true,

    // Activate new client-side router improvements
    clientSegmentCache: true,

    // Enable persistent caching for better performance
    turbopackPersistentCaching: true,

    // Forward browser logs to terminal for easier debugging
    browserDebugInfoInTerminal: true,
  },
};

module.exports = nextConfig;
