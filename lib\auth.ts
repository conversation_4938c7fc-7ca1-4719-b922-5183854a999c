import { supabase } from './supabase'
import { Database } from './database.types'

export type User = Database['public']['Tables']['profiles']['Row']

export const signUp = async (email: string, password: string, userData: {
  first_name: string
  last_name: string
  phone?: string
}) => {
  // First, sign up the user
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  })

  if (error) throw error

  // If email confirmation is disabled, user will be immediately available
  if (data.user && !data.user.email_confirmed_at) {
    console.log('User created but email not confirmed:', data.user.id)
    // Profile will be created after email confirmation or by trigger
    return data
  }

  // If user is immediately confirmed, create profile
  if (data.user && data.user.email_confirmed_at) {
    console.log('Creating profile for confirmed user:', data.user.id)

    // Create profile
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .insert({
        id: data.user.id,
        email,
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone: userData.phone || '',
      })
      .select()

    console.log('Profile creation result:', { profileData, profileError })

    if (profileError) {
      console.error('Profile creation failed:', profileError)
      // Don't throw error here, profile can be created later
      console.warn('Profile creation failed, but user signup succeeded')
    }
  }

  return data
}

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) throw error
  return data
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) return null

  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  // If profile doesn't exist, create it
  if (error && error.code === 'PGRST116') {
    console.log('Profile not found, creating one for user:', user.id)

    const { data: newProfile, error: createError } = await supabase
      .from('profiles')
      .insert({
        id: user.id,
        email: user.email || '',
        first_name: '',
        last_name: '',
        phone: '',
      })
      .select()
      .single()

    if (createError) {
      console.error('Failed to create profile:', createError)
      return null
    }

    return newProfile
  }

  if (error) {
    console.error('Error fetching profile:', error)
    return null
  }

  return profile
}

export const updateProfile = async (userId: string, updates: Partial<User>) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}