import { supabase } from './supabase'
import { Database } from './database.types'

export type User = Database['public']['Tables']['profiles']['Row']

export const signUp = async (email: string, password: string, userData: {
  first_name: string
  last_name: string
  phone?: string
}) => {
  // Sign up the user (trigger will automatically create profile)
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  })

  if (error) throw error

  // If user is created and confirmed, update profile with additional data
  if (data.user) {
    console.log('User created:', data.user.id)

    // Wait a moment for trigger to create profile, then update it
    setTimeout(async () => {
      try {
        await supabase
          .from('profiles')
          .update({
            first_name: userData.first_name,
            last_name: userData.last_name,
            phone: userData.phone || '',
          })
          .eq('id', data.user!.id)

        console.log('Profile updated with user data')
      } catch (updateError) {
        console.error('Failed to update profile:', updateError)
      }
    }, 1000) // 1 second delay to ensure trigger has run
  }

  return data
}

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) throw error
  return data
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) return null

  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (error) {
    console.error('Error fetching profile:', error)
    return null
  }

  return profile
}

export const updateProfile = async (userId: string, updates: Partial<User>) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}