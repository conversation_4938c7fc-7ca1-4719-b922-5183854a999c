import { supabase } from './supabase'
import { Database } from './database.types'

export type User = Database['public']['Tables']['profiles']['Row']

export const signUp = async (email: string, password: string, userData: {
  first_name: string
  last_name: string
  phone?: string
}) => {
  // Sign up the user
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        first_name: userData.first_name,
        last_name: userData.last_name,
        phone: userData.phone || '',
      }
    }
  })

  if (error) throw error

  // If user is created, manually create profile
  if (data.user) {
    console.log('User created:', data.user.id)

    try {
      // Create profile manually
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: data.user.id,
          email: data.user.email || email,
          first_name: userData.first_name,
          last_name: userData.last_name,
          phone: userData.phone || '',
          role: 'customer'
        })

      if (profileError) {
        console.error('Profile creation error:', profileError)
        // Don't throw error, user is already created
      } else {
        console.log('Profile created successfully')
      }
    } catch (profileError) {
      console.error('Failed to create profile:', profileError)
    }
  }

  return data
}

export const signIn = async (email: string, password: string) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  })

  if (error) throw error
  return data
}

export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  if (error) throw error
}

export const getCurrentUser = async () => {
  const { data: { user } } = await supabase.auth.getUser()

  if (!user) return null

  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (error) {
    console.error('Error fetching profile:', error)
    return null
  }

  return profile
}

export const updateProfile = async (userId: string, updates: Partial<User>) => {
  const { data, error } = await supabase
    .from('profiles')
    .update(updates)
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}