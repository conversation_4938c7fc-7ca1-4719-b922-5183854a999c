export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          first_name: string
          last_name: string
          phone: string | null
          role: 'customer' | 'admin'
          avatar_url: string | null
          address_line1: string
          address_line2: string
          city: string
          postal_code: string
          country: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          first_name?: string
          last_name?: string
          phone?: string | null
          role?: 'customer' | 'admin'
          avatar_url?: string | null
          address_line1?: string
          address_line2?: string
          city?: string
          postal_code?: string
          country?: string
        }
        Update: {
          email?: string
          first_name?: string
          last_name?: string
          phone?: string | null
          role?: 'customer' | 'admin'
          avatar_url?: string | null
          address_line1?: string
          address_line2?: string
          city?: string
          postal_code?: string
          country?: string
        }
      }
      categories: {
        Row: {
          id: string
          name: string
          slug: string
          description: string
          image_url: string | null
          parent_id: string | null
          sort_order: number
          is_active: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          slug: string
          description?: string
          image_url?: string | null
          parent_id?: string | null
          sort_order?: number
          is_active?: boolean
        }
        Update: {
          name?: string
          slug?: string
          description?: string
          image_url?: string | null
          parent_id?: string | null
          sort_order?: number
          is_active?: boolean
        }
      }
      products: {
        Row: {
          id: string
          name: string
          slug: string
          description: string
          short_description: string
          price: number
          compare_price: number | null
          sku: string
          category_id: string | null
          material_type: string
          print_time_hours: number
          dimensions: string
          weight_grams: number
          is_active: boolean
          is_featured: boolean
          seo_title: string
          seo_description: string
          created_at: string
          updated_at: string
        }
        Insert: {
          name: string
          slug: string
          description?: string
          short_description?: string
          price: number
          compare_price?: number | null
          sku: string
          category_id?: string | null
          material_type?: string
          print_time_hours?: number
          dimensions?: string
          weight_grams?: number
          is_active?: boolean
          is_featured?: boolean
          seo_title?: string
          seo_description?: string
        }
        Update: {
          name?: string
          slug?: string
          description?: string
          short_description?: string
          price?: number
          compare_price?: number | null
          sku?: string
          category_id?: string | null
          material_type?: string
          print_time_hours?: number
          dimensions?: string
          weight_grams?: number
          is_active?: boolean
          is_featured?: boolean
          seo_title?: string
          seo_description?: string
        }
      }
      product_images: {
        Row: {
          id: string
          product_id: string
          image_url: string
          alt_text: string
          sort_order: number
          is_primary: boolean
          created_at: string
        }
        Insert: {
          product_id: string
          image_url: string
          alt_text?: string
          sort_order?: number
          is_primary?: boolean
        }
        Update: {
          product_id?: string
          image_url?: string
          alt_text?: string
          sort_order?: number
          is_primary?: boolean
        }
      }
      inventory: {
        Row: {
          id: string
          product_id: string
          quantity: number
          reserved_quantity: number
          reorder_level: number
          updated_at: string
        }
        Insert: {
          product_id: string
          quantity?: number
          reserved_quantity?: number
          reorder_level?: number
        }
        Update: {
          quantity?: number
          reserved_quantity?: number
          reorder_level?: number
        }
      }
      carts: {
        Row: {
          id: string
          user_id: string
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
        }
        Update: {
          user_id?: string
        }
      }
      cart_items: {
        Row: {
          id: string
          cart_id: string
          product_id: string
          quantity: number
          price: number
          created_at: string
          updated_at: string
        }
        Insert: {
          cart_id: string
          product_id: string
          quantity?: number
          price: number
        }
        Update: {
          quantity?: number
          price?: number
        }
      }
      orders: {
        Row: {
          id: string
          user_id: string
          order_number: string
          status: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          subtotal: number
          tax_amount: number
          shipping_amount: number
          total_amount: number
          currency: string
          shipping_address: Json | null
          billing_address: Json | null
          notes: string
          created_at: string
          updated_at: string
        }
        Insert: {
          user_id: string
          subtotal: number
          tax_amount?: number
          shipping_amount?: number
          total_amount: number
          currency?: string
          shipping_address?: Json | null
          billing_address?: Json | null
          notes?: string
        }
        Update: {
          status?: 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled'
          subtotal?: number
          tax_amount?: number
          shipping_amount?: number
          total_amount?: number
          shipping_address?: Json | null
          billing_address?: Json | null
          notes?: string
        }
      }
      order_items: {
        Row: {
          id: string
          order_id: string
          product_id: string
          quantity: number
          price: number
          total: number
          created_at: string
        }
        Insert: {
          order_id: string
          product_id: string
          quantity: number
          price: number
          total: number
        }
        Update: {
          quantity?: number
          price?: number
          total?: number
        }
      }
      reviews: {
        Row: {
          id: string
          product_id: string
          user_id: string
          rating: number
          title: string
          comment: string
          is_verified: boolean
          created_at: string
        }
        Insert: {
          product_id: string
          user_id: string
          rating: number
          title?: string
          comment?: string
          is_verified?: boolean
        }
        Update: {
          rating?: number
          title?: string
          comment?: string
          is_verified?: boolean
        }
      }
    }
  }
}