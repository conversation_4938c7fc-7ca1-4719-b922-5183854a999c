'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import ProductCard from '@/components/product/ProductCard'
import { getFeaturedProducts, getCategories } from '@/lib/products'
import { ArrowRight, Truck, Shield, Headphones, Award, Star } from 'lucide-react'

export default function HomePage() {
  const [featuredProducts, setFeaturedProducts] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      const [products, cats] = await Promise.all([
        getFeaturedProducts(8),
        getCategories()
      ])
      setFeaturedProducts(products)
      setCategories(cats.slice(0, 4))
    } catch (error) {
      console.error('Error loading data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const features = [
    {
      icon: Truck,
      title: 'Ücretsiz Kargo',
      description: '500₺ üzeri alışverişlerde ücretsiz kargo'
    },
    {
      icon: Shield,
      title: 'Güvenli Alışveriş',
      description: 'SSL sertifikası ile korumalı ödeme'
    },
    {
      icon: Headphones,
      title: '7/24 Destek',
      description: 'Uzman ekibimiz her zaman yanınızda'
    },
    {
      icon: Award,
      title: 'Kalite Garantisi',
      description: 'Orijinal ürünler, kalite garantisi'
    }
  ]

  const testimonials = [
    {
      name: 'Ahmet Yılmaz',
      rating: 5,
      comment: 'Harika bir platform! Ürünler çok kaliteli ve teslimat super hızlı.',
      product: 'Ender 3 V2 3D Yazıcı'
    },
    {
      name: 'Elif Demir',
      rating: 5,
      comment: 'Filament kalitesi mükemmel, baskı sonuçları çok başarılı.',
      product: 'PLA Filament Paketi'
    },
    {
      name: 'Murat Kaya',
      rating: 5,
      comment: 'Müşteri hizmetleri çok ilgili, her soruma hızlıca cevap aldım.',
      product: 'Reçine 3D Yazıcı'
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 text-white">
        <div className="container mx-auto px-4 py-20 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <h1 className="text-4xl lg:text-6xl font-bold leading-tight">
                3D Baskı
                <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
                  Dünyası
                </span>
              </h1>
              <p className="text-xl text-blue-100 leading-relaxed">
                Türkiye'nin en kapsamlı 3D baskı ürünleri platformu. 
                Yazıcılar, filamentler ve aksesuarlar ile hayallerinizi gerçeğe dönüştürün.
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                  <Link href="/products">
                    Ürünleri Keşfet
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-blue-600">
                  <Link href="/about">Hakkımızda</Link>
                </Button>
              </div>
            </div>
            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="https://images.pexels.com/photos/3846055/pexels-photo-3846055.jpeg"
                  alt="3D Yazıcı"
                  width={600}
                  height={400}
                  className="w-full h-auto"
                />
              </div>
              {/* Floating Stats */}
              <div className="absolute -bottom-6 -left-6 bg-white text-gray-900 p-6 rounded-xl shadow-lg">
                <div className="text-2xl font-bold text-blue-600">1000+</div>
                <div className="text-sm">Mutlu Müşteri</div>
              </div>
              <div className="absolute -top-6 -right-6 bg-white text-gray-900 p-6 rounded-xl shadow-lg">
                <div className="text-2xl font-bold text-green-600">500+</div>
                <div className="text-sm">Ürün Çeşidi</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg mb-4">
                    <feature.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <h3 className="font-semibold text-lg mb-2">{feature.title}</h3>
                  <p className="text-gray-600 text-sm">{feature.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Popüler Kategoriler</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              3D baskı dünyasında ihtiyacınız olan her şey burada. 
              Kategorilerimizi keşfedin ve projelerinize başlayın.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {categories.map((category) => (
              <Link key={category.id} href={`/category/${category.slug}`}>
                <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
                  <div className="relative aspect-square overflow-hidden">
                    <Image
                      src={category.image_url || 'https://images.pexels.com/photos/3846055/pexels-photo-3846055.jpeg'}
                      alt={category.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute inset-0 bg-black/40 group-hover:bg-black/50 transition-colors" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-center text-white">
                        <h3 className="font-bold text-xl mb-2">{category.name}</h3>
                        <p className="text-sm opacity-90">{category.description}</p>
                      </div>
                    </div>
                  </div>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-end mb-12">
            <div>
              <h2 className="text-3xl font-bold mb-4">Öne Çıkan Ürünler</h2>
              <p className="text-gray-600">En popüler ve kaliteli ürünlerimizi keşfedin</p>
            </div>
            <Button asChild variant="outline">
              <Link href="/products">
                Tümünü Gör
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </div>

          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {[...Array(8)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 aspect-square rounded-lg mb-4"></div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Müşterilerimiz Ne Diyor?</h2>
            <p className="text-gray-600">Binlerce mutlu müşterimizden gelen yorumlar</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6">
                <CardContent className="p-0">
                  <div className="flex items-center mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    ))}
                  </div>
                  <blockquote className="text-gray-700 mb-4">
                    "{testimonial.comment}"
                  </blockquote>
                  <div>
                    <div className="font-semibold">{testimonial.name}</div>
                    <div className="text-sm text-gray-500">{testimonial.product}</div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-16 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-4">Haberdar Olun</h2>
          <p className="text-blue-100 mb-8 max-w-2xl mx-auto">
            Yeni ürünler, özel indirimler ve 3D baskı dünyasındaki 
            gelişmelerden haberdar olmak için bültenimize katılın.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="E-posta adresiniz"
              className="flex-1 px-4 py-3 rounded-lg text-gray-900"
            />
            <Button className="bg-white text-blue-600 hover:bg-gray-100">
              Abone Ol
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}