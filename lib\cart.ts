import { supabase } from './supabase'
import { Database } from './database.types'

export type CartItem = Database['public']['Tables']['cart_items']['Row'] & {
  product: Database['public']['Tables']['products']['Row'] & {
    images?: Database['public']['Tables']['product_images']['Row'][]
  }
}

export const getCart = async (userId: string) => {
  // First get or create cart
  let { data: cart } = await supabase
    .from('carts')
    .select('id')
    .eq('user_id', userId)
    .single()

  if (!cart) {
    const { data: newCart, error } = await supabase
      .from('carts')
      .insert({ user_id: userId })
      .select('id')
      .single()

    if (error) throw error
    cart = newCart
  }

  // Get cart items with products
  const { data, error } = await supabase
    .from('cart_items')
    .select(`
      *,
      product:products(
        *,
        images:product_images(*)
      )
    `)
    .eq('cart_id', cart.id)

  if (error) throw error
  return data as CartItem[]
}

export const addToCart = async (userId: string, productId: string, quantity: number = 1) => {
  // Get product price
  const { data: product } = await supabase
    .from('products')
    .select('price')
    .eq('id', productId)
    .single()

  if (!product) throw new Error('Product not found')

  // Get or create cart
  let { data: cart } = await supabase
    .from('carts')
    .select('id')
    .eq('user_id', userId)
    .single()

  if (!cart) {
    const { data: newCart, error } = await supabase
      .from('carts')
      .insert({ user_id: userId })
      .select('id')
      .single()

    if (error) throw error
    cart = newCart
  }

  // Check if item already exists
  const { data: existingItem } = await supabase
    .from('cart_items')
    .select('*')
    .eq('cart_id', cart.id)
    .eq('product_id', productId)
    .single()

  if (existingItem) {
    // Update quantity
    const { data, error } = await supabase
      .from('cart_items')
      .update({ quantity: existingItem.quantity + quantity })
      .eq('id', existingItem.id)
      .select()

    if (error) throw error
    return data
  } else {
    // Add new item
    const { data, error } = await supabase
      .from('cart_items')
      .insert({
        cart_id: cart.id,
        product_id: productId,
        quantity,
        price: product.price
      })
      .select()

    if (error) throw error
    return data
  }
}

export const updateCartItem = async (itemId: string, quantity: number) => {
  if (quantity <= 0) {
    return removeFromCart(itemId)
  }

  const { data, error } = await supabase
    .from('cart_items')
    .update({ quantity })
    .eq('id', itemId)
    .select()

  if (error) throw error
  return data
}

export const removeFromCart = async (itemId: string) => {
  const { error } = await supabase
    .from('cart_items')
    .delete()
    .eq('id', itemId)

  if (error) throw error
}

export const clearCart = async (userId: string) => {
  const { data: cart } = await supabase
    .from('carts')
    .select('id')
    .eq('user_id', userId)
    .single()

  if (!cart) return

  const { error } = await supabase
    .from('cart_items')
    .delete()
    .eq('cart_id', cart.id)

  if (error) throw error
}